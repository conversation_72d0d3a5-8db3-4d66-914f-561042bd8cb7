'use client';

import React, { useEffect, useCallback, useReducer } from 'react';
import styled from 'styled-components';
import { X, Search, Share2, ChevronDown, ChevronUp } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { feedbackUserSearchApi } from '@/services/feedbackService';
import useUserStore from '@/store/userStore';

// Types
interface ShareUser {
  id: number;
  name: string;
  email: string;
  imageUrl?: string;
  departmentName?: string;
  organizationName?: string;
  isLeader?: boolean;
  isAdmin?: boolean;
  isOwner?: boolean;
}

interface Organization {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  description?: string | null;
  memberCount: number;
  userJoinedAt: string;
  isDirectMember: boolean;
  isLeader: boolean;
}

interface ShareFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onShare: (shareData: {
    feedbackUserId: number;
    shareUserIds: number[];
  }) => Promise<void>;
  feedback: any;
  loading?: boolean;
}

// State management types
interface ShareState {
  search: {
    isOpen: boolean;
    isLoading: boolean;
    results: ShareUser[];
    selectedIds: Set<number>;
    filters: {
      organizationId: string;
      departmentId: string;
      name: string;
    };
    organizations: Organization[];
    departments: Department[];
    searchTimeout: NodeJS.Timeout | null;
  };
}

type ShareAction =
  | { type: 'TOGGLE_SEARCH'; payload?: boolean }
  | { type: 'SET_SEARCH_LOADING'; payload: boolean }
  | { type: 'SET_SEARCH_RESULTS'; payload: ShareUser[] }
  | { type: 'TOGGLE_SEARCH_USER'; payload: number }
  | { type: 'CLEAR_SEARCH_SELECTION' }
  | { type: 'UPDATE_SEARCH_FILTERS'; payload: Partial<ShareState['search']['filters']> }
  | { type: 'SET_SEARCH_ORGANIZATIONS'; payload: Organization[] }
  | { type: 'SET_SEARCH_DEPARTMENTS'; payload: Department[] }
  | { type: 'SET_SEARCH_TIMEOUT'; payload: NodeJS.Timeout | null }
  | { type: 'RESET_SEARCH_STATE' };

// Styled Components
const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.$isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: ${appTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h2`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  padding: ${appTheme.spacing.sm};
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.sm};
  color: ${appTheme.colors.text.secondary};
  
  &:hover {
    background: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.lg};
  max-height: 60vh;
  overflow-y: auto;
`;

const FeedbackInfo = styled.div`
  background: ${appTheme.colors.background.light};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.lg};
`;

const FeedbackTitle = styled.div`
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.sm};
`;

const FeedbackPreview = styled.div`
  color: ${appTheme.colors.text.secondary};
  font-size: ${appTheme.typography.fontSizes.sm};
  line-height: 1.4;
`;

const SearchSection = styled.div`
  margin-bottom: ${appTheme.spacing.lg};
`;

const SearchHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${appTheme.spacing.md};
`;

const SearchTitle = styled.h3`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.base};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const ToggleButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  background: none;
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  cursor: pointer;
  color: ${appTheme.colors.text.secondary};
  font-size: ${appTheme.typography.fontSizes.sm};
  
  &:hover {
    background: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.sm};
`;

const FormLabel = styled.label`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
`;

const FormSelect = styled.select`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  transition: ${appTheme.transitions.default};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }

  &:disabled {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.secondary};
    cursor: not-allowed;
  }
`;

const FormInput = styled.input`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  transition: ${appTheme.transitions.default};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }

  &::placeholder {
    color: ${appTheme.colors.text.secondary};
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string; $size?: 'small' | 'medium' }>`
  width: ${props => (props.$size === 'small' ? '32px' : '36px')};
  height: ${props => (props.$size === 'small' ? '32px' : '36px')};
  border-radius: 50%;
  background: ${props =>
    props.$imageUrl ? `url(${props.$imageUrl})` : 'linear-gradient(135deg, #6366f1, #8b5cf6)'};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${appTheme.typography.fontWeights.semibold};
  font-size: ${props => (props.$size === 'small' ? '12px' : '14px')};
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const CustomCheckbox = styled.input.attrs({ type: 'checkbox' })`
  width: 16px;
  height: 16px;
  accent-color: ${appTheme.colors.primary};
  cursor: pointer;
`;

const SelectedCount = styled.div`
  color: ${appTheme.colors.text.secondary};
  font-size: ${appTheme.typography.fontSizes.sm};
  margin-bottom: ${appTheme.spacing.md};
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.lg};
  border: 1px solid ${props => props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  background: ${props => props.$variant === 'primary' ? appTheme.colors.primary : 'transparent'};
  color: ${props => props.$variant === 'primary' ? 'white' : appTheme.colors.text.primary};
  cursor: pointer;
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};

  &:hover {
    background: ${props => props.$variant === 'primary' ? appTheme.colors.primaryHover : appTheme.colors.background.light};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// Helper function to get user initials
const getUserInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

// Initial state for the new state management
const initialShareState: ShareState = {
  search: {
    isOpen: false,
    isLoading: false,
    results: [],
    selectedIds: new Set(),
    filters: {
      organizationId: '',
      departmentId: '',
      name: '',
    },
    organizations: [],
    departments: [],
    searchTimeout: null,
  },
};

// Reducer for managing share state
const shareReducer = (state: ShareState, action: ShareAction): ShareState => {
  switch (action.type) {
    case 'TOGGLE_SEARCH':
      return {
        ...state,
        search: {
          ...state.search,
          isOpen: action.payload !== undefined ? action.payload : !state.search.isOpen,
        },
      };

    case 'SET_SEARCH_LOADING':
      return {
        ...state,
        search: {
          ...state.search,
          isLoading: action.payload,
        },
      };

    case 'SET_SEARCH_RESULTS':
      return {
        ...state,
        search: {
          ...state.search,
          results: action.payload,
        },
      };

    case 'TOGGLE_SEARCH_USER':
      const newSelectedIds = new Set(state.search.selectedIds);
      if (newSelectedIds.has(action.payload)) {
        newSelectedIds.delete(action.payload);
      } else {
        newSelectedIds.add(action.payload);
      }
      return {
        ...state,
        search: {
          ...state.search,
          selectedIds: newSelectedIds,
        },
      };

    case 'CLEAR_SEARCH_SELECTION':
      return {
        ...state,
        search: {
          ...state.search,
          selectedIds: new Set(),
        },
      };

    case 'UPDATE_SEARCH_FILTERS':
      return {
        ...state,
        search: {
          ...state.search,
          filters: {
            ...state.search.filters,
            ...action.payload,
          },
        },
      };

    case 'SET_SEARCH_ORGANIZATIONS':
      return {
        ...state,
        search: {
          ...state.search,
          organizations: action.payload,
        },
      };

    case 'SET_SEARCH_DEPARTMENTS':
      return {
        ...state,
        search: {
          ...state.search,
          departments: action.payload,
        },
      };

    case 'SET_SEARCH_TIMEOUT':
      // Clear existing timeout if any
      if (state.search.searchTimeout) {
        clearTimeout(state.search.searchTimeout);
      }
      return {
        ...state,
        search: {
          ...state.search,
          searchTimeout: action.payload,
        },
      };

    case 'RESET_SEARCH_STATE':
      // Clear timeout if exists
      if (state.search.searchTimeout) {
        clearTimeout(state.search.searchTimeout);
      }
      return {
        ...state,
        search: {
          ...initialShareState.search,
        },
      };

    default:
      return state;
  }
};

export default function ShareFeedbackModal({
  isOpen,
  onClose,
  onShare,
  feedback,
  loading = false,
}: ShareFeedbackModalProps) {
  const { userData } = useUserStore();

  // New state management using useReducer
  const [shareState, dispatch] = useReducer(shareReducer, initialShareState);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      dispatch({ type: 'RESET_SEARCH_STATE' });
      // Initialize organizations from userData when opening
      if (userData?.organizations && userData.organizations.length > 0) {
        const orgs = userData.organizations.map(org => ({
          id: org.id,
          name: org.name,
        }));
        dispatch({ type: 'SET_SEARCH_ORGANIZATIONS', payload: orgs });

        // Auto-select first organization
        const firstOrg = orgs[0];
        dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: { organizationId: firstOrg.id.toString() } });

        // Load departments for first organization
        if (userData.organizations[0]?.departments) {
          dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: userData.organizations[0].departments });
        }
      }
    }
  }, [isOpen, userData?.organizations]);

  // Search for users
  const searchUsers = useCallback(async (filters = shareState.search.filters) => {
    if (!filters.organizationId && !filters.departmentId && !filters.name.trim()) {
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
      return;
    }

    dispatch({ type: 'SET_SEARCH_LOADING', payload: true });
    try {
      const response = await feedbackUserSearchApi.searchUsers(filters);

      if (response?.members) {
        // Transform API response to match our ShareUser interface
        const transformedMembers: ShareUser[] = response.members.map((member: any) => ({
          id: member.user.id,
          name: `${member.user.firstName} ${member.user.lastName}`,
          email: member.user.email,
          imageUrl: member.user.imageUrl,
          departmentName: member.department?.name,
          organizationName: member.department?.organization?.name,
          isLeader: member.isLeader,
          isAdmin: member.isAdmin,
          isOwner: member.isOwner,
        }));

        dispatch({ type: 'SET_SEARCH_RESULTS', payload: transformedMembers });
      } else {
        dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
      }
    } catch (err) {
      console.error('Error searching users:', err);
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
    } finally {
      dispatch({ type: 'SET_SEARCH_LOADING', payload: false });
    }
  }, [shareState.search.filters]);

  // Debounced search function
  const debouncedSearch = useCallback((filters: ShareState['search']['filters']) => {
    if (shareState.search.searchTimeout) {
      clearTimeout(shareState.search.searchTimeout);
    }

    const timeout = setTimeout(() => {
      searchUsers(filters);
    }, 500); // 500ms debounce

    dispatch({ type: 'SET_SEARCH_TIMEOUT', payload: timeout });
  }, [shareState.search.searchTimeout, searchUsers]);

  // Handle search filter changes
  const handleSearchFilterChange = useCallback((field: string, value: string) => {
    const newFilters = { ...shareState.search.filters, [field]: value };
    dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: { [field]: value } });

    // Reset departments when organization changes
    if (field === 'organizationId') {
      dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: { departmentId: '' } });
      dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: [] });

      // Load departments for the selected organization
      if (value && userData?.organizations) {
        const selectedOrg = userData.organizations.find(org => org.id.toString() === value);
        if (selectedOrg?.departments) {
          dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: selectedOrg.departments });
        }
      }
    }

    // Trigger debounced search if we have search criteria
    if (newFilters.organizationId || newFilters.departmentId || newFilters.name.trim()) {
      debouncedSearch(newFilters);
    } else {
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
    }
  }, [shareState.search.filters, userData?.organizations, debouncedSearch]);

  // Toggle user selection in modal
  const toggleUserSelection = useCallback((user: ShareUser) => {
    dispatch({ type: 'TOGGLE_SEARCH_USER', payload: user.id });
  }, []);

  const handleShare = async () => {
    if (shareState.search.selectedIds.size === 0) return;

    try {
      // Find a suitable FeedbackUser record to duplicate
      // Priority: 1) Current user's record, 2) Any existing record, 3) Create from first available
      let sourceFeedbackUser = feedback?.feedbackUsers?.find(
        (fu: any) => fu.userId === userData?.id
      );

      // If current user doesn't have a record, use the first available FeedbackUser record
      if (!sourceFeedbackUser && feedback?.feedbackUsers?.length > 0) {
        sourceFeedbackUser = feedback.feedbackUsers[0];
        console.log('Using first available FeedbackUser record for sharing:', sourceFeedbackUser.id);
      }

      if (!sourceFeedbackUser) {
        console.error('No FeedbackUser record found to duplicate');
        alert('Cannot share feedback: No user record found to duplicate');
        return;
      }

      // Prepare share data with feedbackUserId and shareUserIds
      const shareData = {
        feedbackUserId: sourceFeedbackUser.id,
        shareUserIds: Array.from(shareState.search.selectedIds),
      };

      await onShare(shareData);
      onClose();
    } catch (error) {
      console.error('Error sharing feedback:', error);
    }
  };

  // Toggle user search collapse
  const toggleCollapse = useCallback(() => {
    if (!shareState.search.isOpen) {
      // Initialize organizations from userData when opening
      if (userData?.organizations && userData.organizations.length > 0) {
        const orgs = userData.organizations.map(org => ({
          id: org.id,
          name: org.name,
        }));
        dispatch({ type: 'SET_SEARCH_ORGANIZATIONS', payload: orgs });

        // Auto-select first organization and trigger search
        const firstOrg = orgs[0];
        const initialFilters = {
          organizationId: firstOrg.id.toString(),
          departmentId: '',
          name: '',
        };
        dispatch({ type: 'UPDATE_SEARCH_FILTERS', payload: initialFilters });

        // Load departments for first organization
        if (userData.organizations[0]?.departments) {
          dispatch({ type: 'SET_SEARCH_DEPARTMENTS', payload: userData.organizations[0].departments });
        }

        // Trigger initial search
        searchUsers(initialFilters);
      }
    }

    dispatch({ type: 'TOGGLE_SEARCH' });
  }, [shareState.search.isOpen, userData?.organizations, searchUsers]);

  const getFeedbackPreview = (feedback: any) => {
    const parts = [
      feedback.situation,
      feedback.behavior,
      feedback.impact,
      feedback.actionable,
      feedback.appreciation,
    ].filter(Boolean);
    
    return parts.join(' • ') || 'No content available';
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <Share2 size={20} />
            Share Feedback
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          <FeedbackInfo>
            <FeedbackTitle>
              {feedback?.feedbackType?.displayName || feedback?.feedbackType?.name || 'Feedback'}
            </FeedbackTitle>
            <FeedbackPreview>{getFeedbackPreview(feedback)}</FeedbackPreview>
          </FeedbackInfo>

          <SearchSection>
            <SearchHeader>
              <SearchTitle>Select Users to Share With</SearchTitle>
              <ToggleButton onClick={toggleCollapse}>
                {shareState.search.isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                {shareState.search.isOpen ? 'Hide Search' : 'Find Users'}
              </ToggleButton>
            </SearchHeader>

            {shareState.search.selectedIds.size > 0 && (
              <SelectedCount>
                {shareState.search.selectedIds.size} user{shareState.search.selectedIds.size !== 1 ? 's' : ''} selected
              </SelectedCount>
            )}

            {shareState.search.isOpen && (
              <>
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '12px',
                    marginBottom: '12px',
                  }}
                >
                  <FormGroup>
                    <FormLabel>Organization</FormLabel>
                    <FormSelect
                      value={shareState.search.filters.organizationId}
                      onChange={e => handleSearchFilterChange('organizationId', e.target.value)}
                    >
                      <option value="">All Organizations</option>
                      {shareState.search.organizations.map(org => (
                        <option key={org.id} value={org.id}>
                          {org.name}
                        </option>
                      ))}
                    </FormSelect>
                  </FormGroup>

                  <FormGroup>
                    <FormLabel>Department</FormLabel>
                    <FormSelect
                      value={shareState.search.filters.departmentId}
                      onChange={e => handleSearchFilterChange('departmentId', e.target.value)}
                      disabled={!shareState.search.filters.organizationId}
                    >
                      <option value="">
                        {shareState.search.filters.organizationId
                          ? 'All Departments'
                          : 'Select organization first'}
                      </option>
                      {shareState.search.departments.map(dept => (
                        <option key={dept.id} value={dept.id}>
                          {dept.name}
                        </option>
                      ))}
                    </FormSelect>
                  </FormGroup>

                  <FormGroup style={{ gridColumn: '1 / -1' }}>
                    <FormLabel>Name</FormLabel>
                    <div style={{ position: 'relative' }}>
                      <div
                        style={{
                          position: 'absolute',
                          left: '12px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          color: '#6b7280',
                        }}
                      >
                        <Search size={16} />
                      </div>
                      <FormInput
                        type="text"
                        placeholder="Search by name or email..."
                        value={shareState.search.filters.name}
                        onChange={e => handleSearchFilterChange('name', e.target.value)}
                        style={{ paddingLeft: '36px' }}
                      />
                    </div>
                  </FormGroup>
                </div>

                {shareState.search.isLoading && (
                  <div style={{ textAlign: 'center', padding: '20px', color: '#6b7280' }}>
                    Searching users...
                  </div>
                )}

                {!shareState.search.isLoading && shareState.search.results.length > 0 && (
                  <>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '8px',
                        padding: '4px 8px',
                        backgroundColor: '#f8fafc',
                        borderRadius: '4px',
                      }}
                    >
                      <span style={{ fontSize: '12px', color: '#6b7280' }}>
                        {shareState.search.results.length} result{shareState.search.results.length !== 1 ? 's' : ''} found
                      </span>
                    </div>

                    <div
                      style={{
                        maxHeight: '280px',
                        overflowY: 'auto',
                        border: '1px solid #e5e7eb',
                        borderRadius: '4px',
                      }}
                    >
                      {shareState.search.results.map(user => (
                        <div
                          key={user.id}
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '8px',
                            borderBottom: '1px solid #f3f4f6',
                          }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <CustomCheckbox
                              checked={shareState.search.selectedIds.has(user.id)}
                              onChange={() => toggleUserSelection(user)}
                            />
                            <UserAvatar $imageUrl={user.imageUrl} $size="small">
                              {!user.imageUrl && getUserInitials(user.name)}
                            </UserAvatar>
                            <div>
                              <div
                                style={{
                                  fontWeight: 500,
                                  fontSize: '14px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '6px',
                                  flexWrap: 'wrap',
                                }}
                              >
                                {user.name}
                                {user.isOwner && (
                                  <span
                                    style={{
                                      backgroundColor: '#7c3aed',
                                      color: 'white',
                                      padding: '2px 6px',
                                      borderRadius: '4px',
                                      fontSize: '10px',
                                      fontWeight: 600,
                                    }}
                                  >
                                    Owner
                                  </span>
                                )}
                                {user.isAdmin && (
                                  <span
                                    style={{
                                      backgroundColor: '#dc2626',
                                      color: 'white',
                                      padding: '2px 6px',
                                      borderRadius: '4px',
                                      fontSize: '10px',
                                      fontWeight: 600,
                                    }}
                                  >
                                    Admin
                                  </span>
                                )}
                                {user.isLeader && (
                                  <span
                                    style={{
                                      backgroundColor: '#059669',
                                      color: 'white',
                                      padding: '2px 6px',
                                      borderRadius: '4px',
                                      fontSize: '10px',
                                      fontWeight: 600,
                                    }}
                                  >
                                    Leader
                                  </span>
                                )}
                              </div>
                              <div style={{ fontSize: '12px', color: '#6b7280' }}>{user.email}</div>
                              {(user.departmentName || user.organizationName) && (
                                <div
                                  style={{ fontSize: '11px', color: '#9ca3af', fontStyle: 'italic' }}
                                >
                                  {user.departmentName && `${user.departmentName} • `}
                                  {user.organizationName}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                )}

                {!shareState.search.isLoading &&
                  shareState.search.results.length === 0 &&
                  (shareState.search.filters.organizationId ||
                    shareState.search.filters.departmentId ||
                    shareState.search.filters.name.trim()) && (
                    <div style={{ textAlign: 'center', padding: '20px', color: '#6b7280' }}>
                      No users found matching your search criteria.
                    </div>
                  )}
              </>
            )}
          </SearchSection>
        </ModalBody>

        <ModalFooter>
          <Button $variant="secondary" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            $variant="primary"
            onClick={handleShare}
            disabled={shareState.search.selectedIds.size === 0 || loading}
          >
            {loading ? 'Sharing...' : `Share with ${shareState.search.selectedIds.size} user${shareState.search.selectedIds.size !== 1 ? 's' : ''}`}
          </Button>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}
