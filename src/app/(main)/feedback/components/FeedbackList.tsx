'use client';

import React from 'react';
import styled from 'styled-components';
import { formatDistanceToNow } from 'date-fns';
import {
  User,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Award,
  MessageSquare,
  Share2,
} from 'lucide-react';
import { appTheme } from '@/app/theme';

const ListContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  background: ${appTheme.colors.background.main};
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  color: ${appTheme.colors.text.secondary};
`;

const EmptyContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  text-align: center;
  color: ${appTheme.colors.text.secondary};
  gap: ${appTheme.spacing.md};
`;

const EmptyIcon = styled.div`
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: ${appTheme.colors.background.lighter};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${appTheme.colors.text.tertiary};
`;

const EmptyTitle = styled.h3`
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.secondary};
  margin: 0;
`;

const EmptyDescription = styled.p`
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.tertiary};
  margin: 0;
  max-width: 200px;
`;

const FeedbackItem = styled.div<{ $selected: boolean }>`
  position: relative;
  padding: ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  background: ${props => (props.$selected ? appTheme.colors.primaryLight : 'transparent')};
  border-left: 3px solid ${props => (props.$selected ? appTheme.colors.primary : 'transparent')};

  &:hover {
    background: ${props =>
      props.$selected ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};

    .share-button {
      opacity: 1;
    }
  }

  &:last-child {
    border-bottom: none;
  }
`;

const ShareButton = styled.button`
  position: absolute;
  top: ${appTheme.spacing.sm};
  right: ${appTheme.spacing.sm};
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  padding: ${appTheme.spacing.xs};
  cursor: pointer;
  opacity: 0;
  transition:
    opacity 0.2s ease,
    background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${appTheme.colors.text.secondary};

  &:hover {
    background: ${appTheme.colors.primaryLight};
    color: ${appTheme.colors.primary};
    border-color: ${appTheme.colors.primary};
  }

  &:active {
    transform: scale(0.95);
  }
`;

const FeedbackHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${appTheme.spacing.xs};
`;

const FeedbackType = styled.span<{ $type: string }>`
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  padding: 2px 6px;
  border-radius: ${appTheme.borderRadius.sm};
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: ${props => {
    switch (props.$type.toLowerCase()) {
      case 'private':
        return '#e0f2fe';
      case 'task':
        return '#f3e5f5';
      case 'department':
        return '#e8f5e8';
      case 'organization':
        return '#fff3e0';
      default:
        return appTheme.colors.background.lighter;
    }
  }};
  color: ${props => {
    switch (props.$type.toLowerCase()) {
      case 'private':
        return '#0277bd';
      case 'task':
        return '#7b1fa2';
      case 'department':
        return '#388e3c';
      case 'organization':
        return '#f57c00';
      default:
        return appTheme.colors.text.secondary;
    }
  }};
`;

const FeedbackTime = styled.div`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: ${appTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
`;

const FeedbackCreator = styled.div`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.xs};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
`;

const FeedbackPreview = styled.div`
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.secondary};
  line-height: 1.4;
  margin-bottom: ${appTheme.spacing.xs};
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const FeedbackFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${appTheme.spacing.sm};
`;

const FeedbackStatus = styled.div<{ $status: 'pending' | 'accepted' | 'declined' }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${props => {
    switch (props.$status) {
      case 'accepted':
        return appTheme.colors.success.main;
      case 'declined':
        return appTheme.colors.error.main;
      default:
        return appTheme.colors.text.tertiary;
    }
  }};
`;

const GrowthToken = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.secondary};
`;

type FeedbackTab = 'received' | 'given' | 'shared';

interface FeedbackListProps {
  feedbacks: any[];
  selectedFeedback: any;
  onFeedbackSelect: (feedback: any) => void;
  activeTab: FeedbackTab;
  loading: boolean;
  onShareFeedback?: (feedback: any) => void;
}

export default function FeedbackList({
  feedbacks,
  selectedFeedback,
  onFeedbackSelect,
  activeTab,
  loading,
  onShareFeedback,
}: FeedbackListProps) {
  if (loading) {
    return (
      <ListContainer>
        <LoadingContainer>Loading...</LoadingContainer>
      </ListContainer>
    );
  }

  if (!feedbacks || feedbacks.length === 0) {
    const emptyMessages = {
      received: {
        title: 'No feedback received',
        description: "You haven't received any feedback yet.",
        icon: MessageSquare,
      },
      given: {
        title: 'No feedback given',
        description: "You haven't created any feedback yet.",
        icon: MessageSquare,
      },
      shared: {
        title: 'No shared feedback',
        description: 'No feedback has been shared with you yet.',
        icon: MessageSquare,
      },
    };

    const emptyConfig = emptyMessages[activeTab];
    const EmptyIconComponent = emptyConfig.icon;

    return (
      <ListContainer>
        <EmptyContainer>
          <EmptyIcon>
            <EmptyIconComponent size={32} />
          </EmptyIcon>
          <EmptyTitle>{emptyConfig.title}</EmptyTitle>
          <EmptyDescription>{emptyConfig.description}</EmptyDescription>
        </EmptyContainer>
      </ListContainer>
    );
  }

  const getFeedbackStatus = (feedback: any, tab: FeedbackTab) => {
    if (tab === 'received') {
      const userFeedback = feedback.feedbackUsers?.find((fu: any) => fu.isAccept !== null);
      if (userFeedback?.isAccept === true) return 'accepted';
      if (userFeedback?.isAccept === false || userFeedback?.isDiscard) return 'declined';
      return 'pending';
    }
    return 'pending';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
        return <CheckCircle size={12} />;
      case 'declined':
        return <XCircle size={12} />;
      default:
        return <AlertCircle size={12} />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'Accepted';
      case 'declined':
        return 'Declined';
      default:
        return 'Pending';
    }
  };

  const getFeedbackPreview = (feedback: any) => {
    const parts = [
      feedback.situation,
      feedback.behavior,
      feedback.impact,
      feedback.actionable,
      feedback.appreciation,
    ].filter(Boolean);

    return parts.join(' • ') || 'No content available';
  };

  const getCreatorName = (feedback: any, tab: FeedbackTab) => {
    if (tab === 'received' && feedback.createFrom) {
      return `${feedback.createFrom.firstName} ${feedback.createFrom.lastName}`;
    }
    if (tab === 'given') {
      const assignedUsers = feedback.feedbackUsers
        ?.map((fu: any) => `${fu.feedbackToUser.firstName} ${fu.feedbackToUser.lastName}`)
        .join(', ');
      return assignedUsers || 'No recipients';
    }
    if (tab === 'shared') {
      const assignedUsers = feedback.feedbackUsers
        ?.map((fu: any) => `${fu.shareUser.firstName} ${fu.shareUser.lastName}`)
        .join(', ');
      return assignedUsers || 'No share';
    }
    return 'Unknown';
  };

  return (
    <ListContainer>
      {feedbacks.map(feedback => {
        const isSelected = selectedFeedback?.id === feedback.id;
        const status = getFeedbackStatus(feedback, activeTab);
        const creatorName = getCreatorName(feedback, activeTab);
        const preview = getFeedbackPreview(feedback);

        return (
          <FeedbackItem
            key={feedback.id}
            $selected={isSelected}
            onClick={() => onFeedbackSelect(feedback)}
          >
            {/* Share button - only show if onShareFeedback is provided */}
            {onShareFeedback && (
              <ShareButton
                className="share-button"
                onClick={e => {
                  e.stopPropagation(); // Prevent feedback selection
                  onShareFeedback(feedback);
                }}
                title="Share this feedback"
              >
                <Share2 size={14} />
              </ShareButton>
            )}

            <FeedbackHeader>
              <FeedbackType $type={feedback.feedbackType?.name || 'unknown'}>
                {feedback.feedbackType?.displayName || feedback.feedbackType?.name || 'Unknown'}
              </FeedbackType>
              <FeedbackTime>
                <Clock size={12} />
                {formatDistanceToNow(new Date(feedback.createdAt), { addSuffix: true })}
              </FeedbackTime>
            </FeedbackHeader>

            <FeedbackCreator>
              <User size={14} />
              {activeTab === 'received'
                ? `From: ${creatorName}`
                : activeTab === 'given'
                  ? `To: ${creatorName}`
                  : `Shared by: ${creatorName}`}
            </FeedbackCreator>

            <FeedbackPreview>{preview}</FeedbackPreview>

            <FeedbackFooter>
              {activeTab === 'received' && (
                <FeedbackStatus $status={status}>
                  {getStatusIcon(status)}
                  {getStatusLabel(status)}
                </FeedbackStatus>
              )}

              {feedback.growthToken && feedback.growthToken > 0 && (
                <GrowthToken>
                  <Award size={12} />
                  {feedback.growthToken}
                </GrowthToken>
              )}
            </FeedbackFooter>
          </FeedbackItem>
        );
      })}
    </ListContainer>
  );
}
