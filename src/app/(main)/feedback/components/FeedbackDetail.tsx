'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { formatDistanceToNow } from 'date-fns';
import {
  User,
  CheckCircle,
  XCircle,
  MessageSquare,
  Award,
  Edit3,
  Menu,
  Calendar,
  Share2,
} from 'lucide-react';
import { appTheme } from '@/app/theme';
import { feedbackUserApi, feedbackShareApi } from '@/services/feedbackService';
import useUserStore from '@/store/userStore';
import ShareFeedbackModal from './ShareFeedbackModal';

const DetailContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: ${appTheme.colors.background.main};
`;

const DetailHeader = styled.div`
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.light};
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
`;

const MobileMenuButton = styled.button`
  display: none;

  @media (max-width: ${appTheme.breakpoints.md}) {
    display: flex;
    padding: ${appTheme.spacing.sm};
    background: ${appTheme.colors.background.main};
    border: 1px solid ${appTheme.colors.border};
    border-radius: ${appTheme.borderRadius.md};
    cursor: pointer;
    align-items: center;
    justify-content: center;
  }
`;

const HeaderTitle = styled.h2`
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.sm};
`;

const ActionButton = styled.button<{ $variant?: 'primary' | 'success' | 'danger' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  border: 1px solid
    ${props => {
      switch (props.$variant) {
        case 'primary':
          return appTheme.colors.primary;
        case 'success':
          return appTheme.colors.success.main;
        case 'danger':
          return appTheme.colors.error.main;
        default:
          return appTheme.colors.border;
      }
    }};
  background-color: ${props => {
    switch (props.$variant) {
      case 'primary':
        return appTheme.colors.primary;
      case 'success':
        return appTheme.colors.success.main;
      case 'danger':
        return appTheme.colors.error.main;
      default:
        return appTheme.colors.background.main;
    }
  }};
  color: ${props => {
    switch (props.$variant) {
      case 'primary':
      case 'success':
      case 'danger':
        return 'white';
      default:
        return appTheme.colors.text.secondary;
    }
  }};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  transition: ${appTheme.transitions.default};

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const DetailContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${appTheme.spacing.lg};

  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: ${appTheme.colors.text.secondary};
  gap: ${appTheme.spacing.md};
`;

const EmptyIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: ${appTheme.colors.background.lighter};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${appTheme.colors.text.tertiary};
`;

const FeedbackMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.xl};
  padding: ${appTheme.spacing.md};
  background: ${appTheme.colors.background.lighter};
  border-radius: ${appTheme.borderRadius.md};
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.secondary};
`;

const FeedbackSection = styled.div`
  margin-bottom: ${appTheme.spacing.xl};
`;

const SectionTitle = styled.h3`
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin: 0 0 ${appTheme.spacing.md} 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const SectionContent = styled.div`
  font-size: ${appTheme.typography.fontSizes.base};
  line-height: 1.6;
  color: ${appTheme.colors.text.secondary};
  background: ${appTheme.colors.background.light};
  padding: ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  border-left: 4px solid ${appTheme.colors.primary};
`;

const ReflectionSection = styled.div`
  margin-top: ${appTheme.spacing.xl};
  padding: ${appTheme.spacing.lg};
  background: ${appTheme.colors.background.lighter};
  border-radius: ${appTheme.borderRadius.md};
`;

const ReflectionTextarea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.base};
  font-family: inherit;
  resize: vertical;
  background: ${appTheme.colors.background.main};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }
`;

const ReflectionActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.sm};
  margin-top: ${appTheme.spacing.md};
  justify-content: flex-end;
`;

type FeedbackTab = 'received' | 'given' | 'shared';

interface FeedbackDetailProps {
  feedback: any;
  activeTab: FeedbackTab;
  onRefresh: () => void;
  onMobileSidebarOpen: () => void;
}

export default function FeedbackDetail({
  feedback,
  activeTab,
  onRefresh,
  onMobileSidebarOpen,
}: FeedbackDetailProps) {
  const { userData } = useUserStore();
  const [reflection, setReflection] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAcceptReflection, setShowAcceptReflection] = useState(false);
  const [showDiscardReflection, setShowDiscardReflection] = useState(false);
  const [reflectionError, setReflectionError] = useState('');
  const [showShareModal, setShowShareModal] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  if (!feedback) {
    return (
      <DetailContainer>
        <DetailHeader>
          <HeaderLeft>
            <MobileMenuButton onClick={onMobileSidebarOpen}>
              <Menu size={20} />
            </MobileMenuButton>
            <HeaderTitle>Select Feedback</HeaderTitle>
          </HeaderLeft>
        </DetailHeader>
        <DetailContent>
          <EmptyState>
            <EmptyIcon>
              <MessageSquare size={40} />
            </EmptyIcon>
            <div>
              <h3>No feedback selected</h3>
              <p>Choose a feedback from the list to view details</p>
            </div>
          </EmptyState>
        </DetailContent>
      </DetailContainer>
    );
  }

  const handleAcceptFeedback = async () => {
    if (!feedback || activeTab !== 'received' || !currentUserId) return;

    // Validate reflection is required
    if (!reflection.trim()) {
      setReflectionError('Reflection is required when accepting feedback');
      return;
    }

    try {
      setIsSubmitting(true);
      setReflectionError('');

      await feedbackUserApi.updateFeedbackUserStatus({
        feedbackId: feedback.id,
        userId: currentUserId,
        isAccept: true,
        isDiscard: false,
        reflection: reflection.trim(),
      });

      // Reset form state
      setReflection('');
      setShowAcceptReflection(false);
      onRefresh();
    } catch (error) {
      console.error('Failed to accept feedback:', error);
      setReflectionError('Failed to accept feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDiscardFeedback = async () => {
    if (!feedback || activeTab !== 'received' || !currentUserId) return;

    // Validate reflection is required
    if (!reflection.trim()) {
      setReflectionError('Reflection is required when discarding feedback');
      return;
    }

    try {
      setIsSubmitting(true);
      setReflectionError('');

      await feedbackUserApi.updateFeedbackUserStatus({
        feedbackId: feedback.id,
        userId: currentUserId,
        isAccept: false,
        isDiscard: true,
        reflection: reflection.trim(),
      });

      // Reset form state
      setReflection('');
      setShowDiscardReflection(false);
      onRefresh();
    } catch (error) {
      console.error('Failed to discard feedback:', error);
      setReflectionError('Failed to discard feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleShareFeedback = async (shareData: {
    feedbackUserId: number;
    shareUserIds: number[];
  }) => {
    setIsSharing(true);
    try {
      await feedbackShareApi.shareFeedback(shareData.feedbackUserId, shareData.shareUserIds);
      onRefresh(); // Refresh to show updated data
    } catch (error) {
      console.error('Error sharing feedback:', error);
      throw error; // Let the modal handle the error display
    } finally {
      setIsSharing(false);
    }
  };

  // Get current user's feedback status
  const currentUserId = userData?.id;

  // Debug: Log the feedback data structure
  console.log('FeedbackDetail Debug:', {
    activeTab,
    currentUserId,
    feedbackUsers: feedback.feedbackUsers,
    feedbackUsersLength: feedback.feedbackUsers?.length,
  });

  // For 'received' tab, feedbackUsers array is already filtered to current user by API
  // For other tabs, we need to find the current user's record
  const currentUserFeedback =
    activeTab === 'received'
      ? feedback.feedbackUsers?.[0] // API filters to current user only
      : feedback.feedbackUsers?.find((fu: any) => fu.user?.id === currentUserId);

  // Check if feedback has already been processed (accepted or discarded)
  const hasBeenProcessed =
    currentUserFeedback &&
    (currentUserFeedback.isAccept !== null || currentUserFeedback.isDiscard === true);

  const isAccepted = currentUserFeedback?.isAccept === true;
  const isDiscarded = currentUserFeedback?.isDiscard === true;

  // Debug: Log the status determination
  console.log('Status Debug:', {
    currentUserFeedback,
    isAccepted,
    isDiscarded,
    hasBeenProcessed,
  });

  return (
    <DetailContainer>
      <DetailHeader>
        <HeaderLeft>
          <MobileMenuButton onClick={onMobileSidebarOpen}>
            <Menu size={20} />
          </MobileMenuButton>
          <HeaderTitle>
            {feedback.feedbackType?.displayName || feedback.feedbackType?.name || 'Feedback'}
          </HeaderTitle>
        </HeaderLeft>
        <HeaderActions>
          {/* Share button - available for all users who can view the feedback */}
          <ActionButton
            onClick={() => setShowShareModal(true)}
            disabled={isSubmitting || isSharing}
            title="Share this feedback with other users"
          >
            <Share2 size={16} />
            Share
          </ActionButton>

          {activeTab === 'received' && (
            <>
              <ActionButton
                $variant="danger"
                onClick={() => setShowDiscardReflection(true)}
                disabled={isSubmitting}
              >
                <XCircle size={16} />
                Discard
              </ActionButton>
              <ActionButton
                $variant="success"
                onClick={() => setShowAcceptReflection(true)}
                disabled={isSubmitting}
              >
                <CheckCircle size={16} />
                Accept
              </ActionButton>
            </>
          )}
        </HeaderActions>
      </DetailHeader>

      <DetailContent>
        <FeedbackMeta>
          <MetaItem>
            <User size={16} />
            {activeTab === 'received'
              ? `From: ${feedback.createFrom?.firstName} ${feedback.createFrom?.lastName}`
              : `Created by you`}
          </MetaItem>
          <MetaItem>
            <Calendar size={16} />
            {formatDistanceToNow(new Date(feedback.createdAt), { addSuffix: true })}
          </MetaItem>
          {feedback.growthToken && feedback.growthToken > 0 && (
            <MetaItem>
              <Award size={16} />
              {feedback.growthToken} Growth Token{feedback.growthToken > 1 ? 's' : ''}
            </MetaItem>
          )}
          {isAccepted && (
            <MetaItem style={{ color: appTheme.colors.success.main }}>
              <CheckCircle size={16} />
              Accepted
            </MetaItem>
          )}
          {isDiscarded && (
            <MetaItem style={{ color: appTheme.colors.error.main }}>
              <XCircle size={16} />
              Discarded
            </MetaItem>
          )}
        </FeedbackMeta>

        {feedback.situation && (
          <FeedbackSection>
            <SectionTitle>Situation</SectionTitle>
            <SectionContent>{feedback.situation}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.behavior && (
          <FeedbackSection>
            <SectionTitle>Behavior</SectionTitle>
            <SectionContent>{feedback.behavior}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.impact && (
          <FeedbackSection>
            <SectionTitle>Impact</SectionTitle>
            <SectionContent>{feedback.impact}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.actionable && (
          <FeedbackSection>
            <SectionTitle>Actionable</SectionTitle>
            <SectionContent>{feedback.actionable}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.appreciation && (
          <FeedbackSection>
            <SectionTitle>Appreciation</SectionTitle>
            <SectionContent>{feedback.appreciation}</SectionContent>
          </FeedbackSection>
        )}

        {/* Show reflection for received feedback */}
        {activeTab === 'received' && currentUserFeedback?.reflection && (
          <FeedbackSection>
            <SectionTitle>Your Reflection</SectionTitle>
            <SectionContent>{currentUserFeedback.reflection}</SectionContent>
          </FeedbackSection>
        )}

        {/* Show recipient reflections and status for given feedback */}
        {activeTab === 'given' && feedback.feedbackUsers && feedback.feedbackUsers.length > 0 && (
          <FeedbackSection>
            <SectionTitle>Recipient Responses</SectionTitle>
            {feedback.feedbackUsers.map((feedbackUser: any) => {
              const isAccepted = feedbackUser.isAccept === true;
              const isDiscarded = feedbackUser.isDiscard === true;
              const hasResponse = isAccepted || isDiscarded;
              const isPending = !hasResponse;
              if (feedbackUser.isShare === true) return null;
              return (
                <div
                  key={feedbackUser.id}
                  style={{
                    marginBottom: appTheme.spacing.lg,
                    padding: appTheme.spacing.md,
                    background: appTheme.colors.background.light,
                    borderRadius: appTheme.borderRadius.md,
                    border: `1px solid ${appTheme.colors.border}`,
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: appTheme.spacing.sm,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: appTheme.spacing.sm,
                        fontWeight: appTheme.typography.fontWeights.medium,
                        color: appTheme.colors.text.primary,
                      }}
                    >
                      <User size={16} />
                      {feedbackUser.user?.firstName} {feedbackUser.user?.lastName}
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: appTheme.spacing.xs,
                        padding: `${appTheme.spacing.xs} ${appTheme.spacing.sm}`,
                        borderRadius: appTheme.borderRadius.sm,
                        fontSize: appTheme.typography.fontSizes.sm,
                        fontWeight: appTheme.typography.fontWeights.medium,
                        background: isPending
                          ? appTheme.colors.background.lighter
                          : isAccepted
                            ? appTheme.colors.success.light
                            : appTheme.colors.error.light,
                        color: isPending
                          ? appTheme.colors.text.secondary
                          : isAccepted
                            ? appTheme.colors.success.main
                            : appTheme.colors.error.main,
                      }}
                    >
                      {isPending ? (
                        <>
                          <Calendar size={14} />
                          Pending
                        </>
                      ) : isAccepted ? (
                        <>
                          <CheckCircle size={14} />
                          Accepted
                        </>
                      ) : (
                        <>
                          <XCircle size={14} />
                          Discarded
                        </>
                      )}
                    </div>
                  </div>

                  {feedbackUser.reflection && (
                    <div
                      style={{
                        marginTop: appTheme.spacing.sm,
                        padding: appTheme.spacing.sm,
                        background: appTheme.colors.background.main,
                        borderRadius: appTheme.borderRadius.sm,
                        borderLeft: `3px solid ${
                          isAccepted ? appTheme.colors.success.main : appTheme.colors.error.main
                        }`,
                      }}
                    >
                      <div
                        style={{
                          fontSize: appTheme.typography.fontSizes.xs,
                          color: appTheme.colors.text.tertiary,
                          marginBottom: appTheme.spacing.xs,
                          fontWeight: appTheme.typography.fontWeights.medium,
                        }}
                      >
                        {isAccepted ? 'Reflection:' : 'Reason for discarding:'}
                      </div>
                      <div
                        style={{
                          fontSize: appTheme.typography.fontSizes.sm,
                          color: appTheme.colors.text.secondary,
                          lineHeight: 1.5,
                        }}
                      >
                        {feedbackUser.reflection}
                      </div>
                    </div>
                  )}

                  {isPending && (
                    <div
                      style={{
                        marginTop: appTheme.spacing.sm,
                        fontSize: appTheme.typography.fontSizes.sm,
                        color: appTheme.colors.text.tertiary,
                        fontStyle: 'italic',
                      }}
                    >
                      Waiting for response...
                    </div>
                  )}
                </div>
              );
            })}
          </FeedbackSection>
        )}

        {/* Accept Reflection Section */}
        {showAcceptReflection && (
          <ReflectionSection>
            <SectionTitle>
              <Edit3 size={20} />
              Add Your Reflection (Required)
            </SectionTitle>
            <ReflectionTextarea
              value={reflection}
              onChange={e => {
                setReflection(e.target.value);
                if (reflectionError) setReflectionError('');
              }}
              placeholder="Please share your thoughts about this feedback... (Required)"
            />
            {reflectionError && (
              <div
                style={{
                  color: appTheme.colors.error.main,
                  fontSize: appTheme.typography.fontSizes.sm,
                  marginTop: appTheme.spacing.xs,
                }}
              >
                {reflectionError}
              </div>
            )}
            <ReflectionActions>
              <ActionButton
                onClick={() => {
                  setShowAcceptReflection(false);
                  setReflection('');
                  setReflectionError('');
                }}
              >
                Cancel
              </ActionButton>
              <ActionButton
                $variant="success"
                onClick={handleAcceptFeedback}
                disabled={isSubmitting || !reflection.trim()}
              >
                <CheckCircle size={16} />
                {isSubmitting ? 'Accepting...' : 'Accept Feedback'}
              </ActionButton>
            </ReflectionActions>
          </ReflectionSection>
        )}

        {/* Discard Reflection Section */}
        {showDiscardReflection && (
          <ReflectionSection>
            <SectionTitle>
              <Edit3 size={20} />
              Please explain why you're discarding this feedback (Required)
            </SectionTitle>
            <ReflectionTextarea
              value={reflection}
              onChange={e => {
                setReflection(e.target.value);
                if (reflectionError) setReflectionError('');
              }}
              placeholder="Please explain your reason for discarding this feedback... (Required)"
            />
            {reflectionError && (
              <div
                style={{
                  color: appTheme.colors.error.main,
                  fontSize: appTheme.typography.fontSizes.sm,
                  marginTop: appTheme.spacing.xs,
                }}
              >
                {reflectionError}
              </div>
            )}
            <ReflectionActions>
              <ActionButton
                onClick={() => {
                  setShowDiscardReflection(false);
                  setReflection('');
                  setReflectionError('');
                }}
              >
                Cancel
              </ActionButton>
              <ActionButton
                $variant="danger"
                onClick={handleDiscardFeedback}
                disabled={isSubmitting || !reflection.trim()}
              >
                <XCircle size={16} />
                {isSubmitting ? 'Discarding...' : 'Discard Feedback'}
              </ActionButton>
            </ReflectionActions>
          </ReflectionSection>
        )}
      </DetailContent>

      {/* Share Feedback Modal */}
      <ShareFeedbackModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        onShare={handleShareFeedback}
        feedback={feedback}
        loading={isSharing}
      />
    </DetailContainer>
  );
}
